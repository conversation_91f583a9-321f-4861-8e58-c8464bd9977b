// Standard Library Headers
#include <time.h>
#include <sys/time.h>

// ESP-IDF Headers
#include "esp_netif_sntp.h"
#include "esp_sntp.h"
#include "esp_log.h"

// LWIP Headers
#include "lwip/ip_addr.h"

// FreeRTOS Headers
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#define TAG "sntp"

#define SLEEP_PERIOD_MS 3600000 // 1 hour in milliseconds

/**
 * @brief Synchronizes system time using SNTP.
 *
 * This function waits for the system time to be set by SNTP, retrieves the current time,
 * and verifies if the time is valid. If the time is invalid (year < 2023), it logs a warning
 * and restarts the ESP device.
 *
 * @return Returns true if time synchronization is successful, false otherwise.
 */
bool synchronize_time(void)
{
    // Variables to store the current time and retry attempts
    time_t now = 0;
    struct tm timeinfo = { 0 };
    int retry = 0;
    const int retry_count = 7; // Maximum number of retries for time synchronization
    
    // Wait for the SNTP synchronization to complete, with a timeout of 2000 ms per attempt
    while (esp_netif_sntp_sync_wait(2000 / portTICK_PERIOD_MS) == ESP_ERR_TIMEOUT && ++retry < retry_count) {
        ESP_LOGI(TAG, "Waiting for system time to be set... (%d/%d)", retry, retry_count);
    }

    // Get the current time in seconds since the Unix epoch
    time(&now);

    // Format and print the current time
    char strftime_buf[64];
    setenv("TZ", "UTC", 1); // Set the timezone to UTC
    tzset(); // Apply the timezone settings

    // Convert the time to a local time structure
    localtime_r(&now, &timeinfo);

    // Format the time as a string and store it in strftime_buf
    strftime(strftime_buf, sizeof(strftime_buf), "%c", &timeinfo);

    // Log the current date and time
    ESP_LOGI(TAG, "The current date/time is: %s", strftime_buf);

    // Check if the year is less than 2023
    if (timeinfo.tm_year + 1900 < 2023) {
        ESP_LOGW(TAG, "The current date/time is not valid. Please check your network settings.");
        esp_restart();
        return false;
    }

    // Time synchronization was successful
    return true;
}

/**
 * @brief FreeRTOS task to periodically synchronize system time.
 *
 * This task sleeps for 24 hours, broken down into 1-hour intervals, 
 * and then calls the time synchronization function.
 *
 * @param pvParameters Task parameters (not used).
 */
void time_sync_task(void* pvParameters) {
    while (1) {
        ESP_LOGI(TAG, "Time sync task sleeping for 24 hours...");

        // Sleep for 24 hours in 1-hour intervals to avoid long blocking delays
        for (int i = 0; i < 24; i++) {
            vTaskDelay(pdMS_TO_TICKS(SLEEP_PERIOD_MS)); // Sleep for 1 hour, repeat 24 times
        }

        // Perform time synchronization after 24 hours
        synchronize_time();
    }
}

/**
 * @brief Creates and starts the SNTP time synchronization timer task.
 *
 * This function creates a FreeRTOS task that synchronizes the time every 24 hours.
 *
 * @return Returns true on success, false on failure.
 */
bool sntp_client_init(void) {
    ESP_LOGI(TAG, "Initializing SNTP");
    esp_sntp_config_t config = ESP_NETIF_SNTP_DEFAULT_CONFIG(CONFIG_SNTP_TIME_SERVER);
    esp_netif_sntp_init(&config);
    esp_netif_sntp_start();

    // Create a new FreeRTOS task for time synchronization with PSRAM stack
    BaseType_t result = xTaskCreateWithCaps(time_sync_task, "TimeSyncTask", 4096, NULL, 5, NULL, MALLOC_CAP_SPIRAM);
    if (result != pdPASS) {
        ESP_LOGW(TAG, "Failed to create time sync task with PSRAM stack, trying internal RAM");
        result = xTaskCreate(time_sync_task, "TimeSyncTask", 4096, NULL, 5, NULL);
    } else {
        ESP_LOGI(TAG, "Time sync task created with PSRAM stack");
    }

    if (result != pdPASS) {
        ESP_LOGE(TAG, "Failed to create time sync task.");
        return false;
    }

    // Execute synchronization time once immediately
    return synchronize_time();
}
